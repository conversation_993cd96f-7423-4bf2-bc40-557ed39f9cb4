package data

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
)

// AIRepo implements the AI repository
type AIRepo struct {
	data   *Data
	config *conf.AI
	log    *log.Helper
}

// NewAIRepo creates a new AI repository
func NewAIRepo(data *Data, config *conf.AI, logger log.Logger) biz.AIRepo {
	return &AIRepo{
		data:   data,
		config: config,
		log:    log.NewHelper(logger),
	}
}

// Chat processes a chat request using AI models
func (r *AIRepo) Chat(ctx context.Context, req *biz.ChatRequest) (*biz.ChatResponse, error) {
	r.log.WithContext(ctx).Infof("Processing chat with model: %s", req.Model)
	
	var endpoint string
	var modelName string
	
	// Select model configuration
	switch req.Model {
	case "gemma-3-4b-it":
		endpoint = r.config.Gemma.Endpoint
		modelName = r.config.Gemma.ModelName
	case "bielik-v3":
		endpoint = r.config.Bielik.Endpoint
		modelName = r.config.Bielik.ModelName
	default:
		return nil, biz.ErrAIModelNotFound
	}
	
	// Prepare request payload
	payload := map[string]interface{}{
		"model":  modelName,
		"prompt": req.Message,
		"stream": false,
	}
	
	// Add context if provided
	if len(req.Context) > 0 {
		payload["context"] = strings.Join(req.Context, "\n")
	}
	
	// Make HTTP request to AI model
	response, err := r.makeAIRequest(ctx, endpoint, payload)
	if err != nil {
		return nil, err
	}
	
	return &biz.ChatResponse{
		Response:   response["response"].(string),
		ModelUsed:  req.Model,
		TokensUsed: int32(response["tokens_used"].(float64)),
	}, nil
}// Analyze processes an analysis request
func (r *AIRepo) Analyze(ctx context.Context, req *biz.AnalyzeRequest) (*biz.AnalyzeResponse, error) {
	r.log.WithContext(ctx).Infof("Processing analysis: %s", req.AnalysisType)
	
	// Select model for analysis (prefer Bielik for analysis tasks)
	endpoint := r.config.Bielik.Endpoint
	modelName := r.config.Bielik.ModelName
	
	// Prepare analysis prompt
	prompt := fmt.Sprintf("Analyze the following content for %s:\n\n%s", req.AnalysisType, req.Content)
	
	payload := map[string]interface{}{
		"model":  modelName,
		"prompt": prompt,
		"stream": false,
	}
	
	response, err := r.makeAIRequest(ctx, endpoint, payload)
	if err != nil {
		return nil, err
	}
	
	return &biz.AnalyzeResponse{
		Analysis:   response["response"].(string),
		Confidence: 0.85, // Mock confidence score
		Metadata: map[string]string{
			"model":         req.Model,
			"analysis_type": req.AnalysisType,
		},
	}, nil
}

// ListModels returns available AI models
func (r *AIRepo) ListModels(ctx context.Context) ([]*biz.AIModel, error) {
	models := []*biz.AIModel{
		{
			Name:      "gemma-3-4b-it",
			Type:      "chat",
			Available: true,
			Endpoint:  r.config.Gemma.Endpoint,
		},
		{
			Name:      "bielik-v3",
			Type:      "analysis",
			Available: true,
			Endpoint:  r.config.Bielik.Endpoint,
		},
	}
	
	return models, nil
}

// IsModelAvailable checks if a model is available
func (r *AIRepo) IsModelAvailable(ctx context.Context, modelName string) (bool, error) {
	models, err := r.ListModels(ctx)
	if err != nil {
		return false, err
	}
	
	for _, model := range models {
		if model.Name == modelName && model.Available {
			return true, nil
		}
	}
	
	return false, nil
}// makeAIRequest makes HTTP request to AI model endpoint
func (r *AIRepo) makeAIRequest(ctx context.Context, endpoint string, payload map[string]interface{}) (map[string]interface{}, error) {
	// Mock implementation - in real scenario, make actual HTTP request
	// This would use http.Client to call the AI model endpoint
	
	r.log.WithContext(ctx).Infof("Making AI request to: %s", endpoint)
	
	// Mock response for demonstration
	mockResponse := map[string]interface{}{
		"response":    "This is a mock AI response. In production, this would be the actual AI model response.",
		"tokens_used": float64(150),
		"model":       payload["model"],
	}
	
	return mockResponse, nil
}