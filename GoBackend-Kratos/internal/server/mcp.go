package server

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/metoro-io/mcp-golang"
	"github.com/metoro-io/mcp-golang/transport/stdio"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
)

// MCPServer wraps the MCP server functionality
type MCPServer struct {
	server     *mcp_golang.Server
	customerUc *biz.CustomerUsecase
	jobUc      *biz.JobUsecase
	aiUc       *biz.AIUsecase
	log        *log.Helper
}

// HVAC Tool Arguments
type CreateCustomerArgs struct {
	Name    string `json:"name" jsonschema:"required,description=Customer name"`
	Email   string `json:"email" jsonschema:"required,description=Customer email address"`
	Phone   string `json:"phone" jsonschema:"description=Customer phone number"`
	Address string `json:"address" jsonschema:"description=Customer address"`
}

type CreateJobArgs struct {
	CustomerID  int64  `json:"customer_id" jsonschema:"required,description=Customer ID"`
	Title       string `json:"title" jsonschema:"required,description=Job title"`
	Description string `json:"description" jsonschema:"description=Job description"`
	Priority    string `json:"priority" jsonschema:"description=Job priority (low, medium, high, urgent)"`
}

type AIAnalyzeArgs struct {
	Content      string `json:"content" jsonschema:"required,description=Content to analyze"`
	AnalysisType string `json:"analysis_type" jsonschema:"description=Type of analysis to perform"`
	Model        string `json:"model" jsonschema:"description=AI model to use"`
}

// NewMCPServer creates a new MCP server
func NewMCPServer(
	c *conf.MCP,
	customerUc *biz.CustomerUsecase,
	jobUc *biz.JobUsecase,
	aiUc *biz.AIUsecase,
	logger log.Logger,
) *MCPServer {
	transport := stdio.NewStdioServerTransport()
	server := mcp_golang.NewServer(transport)
	
	mcpServer := &MCPServer{
		server:     server,
		customerUc: customerUc,
		jobUc:      jobUc,
		aiUc:       aiUc,
		log:        log.NewHelper(logger),
	}
	
	// Register tools if enabled
	if c.Tools.Enabled {
		mcpServer.registerTools(c.Tools)
	}
	
	return mcpServer
}// registerTools registers MCP tools
func (s *MCPServer) registerTools(toolsConfig *conf.MCP_Tools) {
	if toolsConfig.HvacTools {
		s.registerHVACTools()
	}
	if toolsConfig.EmailTools {
		s.registerEmailTools()
	}
}

// registerHVACTools registers HVAC-specific tools
func (s *MCPServer) registerHVACTools() {
	// Create Customer Tool
	s.server.RegisterTool("create_customer", "Create a new HVAC customer", 
		func(args CreateCustomerArgs) (*mcp_golang.ToolResponse, error) {
			customer := &biz.Customer{
				Name:    args.Name,
				Email:   args.Email,
				Phone:   args.Phone,
				Address: args.Address,
			}
			
			result, err := s.customerUc.CreateCustomer(context.Background(), customer)
			if err != nil {
				return nil, err
			}
			
			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("Customer created successfully: ID=%d, Name=%s", result.ID, result.Name)),
			), nil
		})

	// Create Job Tool
	s.server.RegisterTool("create_job", "Create a new HVAC job",
		func(args CreateJobArgs) (*mcp_golang.ToolResponse, error) {
			job := &biz.Job{
				CustomerID:  args.CustomerID,
				Title:       args.Title,
				Description: args.Description,
				Priority:    args.Priority,
			}
			
			result, err := s.jobUc.CreateJob(context.Background(), job)
			if err != nil {
				return nil, err
			}
			
			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("Job created successfully: ID=%d, Title=%s", result.ID, result.Title)),
			), nil
		})
}

// registerEmailTools registers email-related tools
func (s *MCPServer) registerEmailTools() {
	// AI Analyze Tool
	s.server.RegisterTool("ai_analyze", "Analyze content using AI",
		func(args AIAnalyzeArgs) (*mcp_golang.ToolResponse, error) {
			req := &biz.AnalyzeRequest{
				Content:      args.Content,
				AnalysisType: args.AnalysisType,
				Model:        args.Model,
			}
			
			result, err := s.aiUc.Analyze(context.Background(), req)
			if err != nil {
				return nil, err
			}
			
			return mcp_golang.NewToolResponse(
				mcp_golang.NewTextContent(fmt.Sprintf("Analysis: %s (Confidence: %.2f)", result.Analysis, result.Confidence)),
			), nil
		})
}// Serve starts the MCP server
func (s *MCPServer) Serve() error {
	s.log.Info("Starting MCP server...")
	return s.server.Serve()
}

// Stop stops the MCP server
func (s *MCPServer) Stop() error {
	s.log.Info("Stopping MCP server...")
	// MCP server doesn't have explicit stop method in current implementation
	return nil
}