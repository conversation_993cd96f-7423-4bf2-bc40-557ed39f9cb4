# 🚀 GoBackend HVAC Kratos

## 🎯 Ultra-Modern HVAC CRM with Kratos Framework

### ✨ Features

- 🔥 **Kratos Framework** - Production-ready microservice architecture
- 🤖 **AI Integration** - Gemma-3-4b-it & Bielik V3 models
- 🛠️ **MCP Protocol** - Type-safe LLM tool integration
- 📧 **BillionMail Ready** - Advanced email management
- 🗄️ **PostgreSQL** - Robust database with GORM
- ⚡ **Redis Cache** - High-performance caching
- 🔍 **J<PERSON>ger Tracing** - Distributed tracing
- 📊 **Prometheus Metrics** - Comprehensive monitoring
- 🐳 **Docker** - Containerized deployment
- 🔧 **gRPC & HTTP** - Dual transport protocols

### 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP/gRPC     │    │   MCP Server    │    │   AI Models     │
│   Transport     │    │   (LLM Tools)   │    │ Gemma/Bielik    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
┌─────────────────────────────────────────────────────────────────┐
│                    Service Layer                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ HVAC Service│  │ AI Service  │  │ MCP Service │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                   Business Logic                               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ Customer UC │  │   Job UC    │  │   AI UC     │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
         │
┌─────────────────────────────────────────────────────────────────┐
│                    Data Layer                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ PostgreSQL  │  │   Redis     │  │ AI Models   │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
```### 🚀 Quick Start

#### Prerequisites
- Go 1.24+
- Docker & Docker Compose
- Protocol Buffers compiler

#### 1. Clone & Setup
```bash
git clone <repository>
cd GoBackend-Kratos
make init  # Install required tools
```

#### 2. Generate Code
```bash
make all   # Generate protobuf, wire, etc.
```

#### 3. Start with Docker
```bash
docker-compose up -d
```

#### 4. Development Mode
```bash
# Start dependencies
docker-compose up -d postgres redis jaeger

# Run locally
go run cmd/server/main.go -conf ./configs
```

### 🔧 API Endpoints

#### HTTP REST API
- `GET /api/v1/customers` - List customers
- `POST /api/v1/customers` - Create customer
- `GET /api/v1/jobs` - List jobs
- `POST /api/v1/jobs` - Create job
- `POST /api/v1/ai/chat` - AI chat
- `POST /api/v1/ai/analyze` - AI analysis

#### gRPC Services
- `HVACService` - Customer & Job management
- `AIService` - AI model interactions

#### MCP Tools (for LLM)
- `create_customer` - Create HVAC customer
- `create_job` - Create HVAC job
- `ai_analyze` - Analyze content with AI

### 🎯 Next Steps: BillionMail Integration

Ready for BillionMail email server integration! 📧🔥